package com.coocare.ai.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.UUID;

/**
 * 第三方应用凭证生成器
 * 用于生成和验证第三方应用的appid和appkey
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
@Component
public class AppCredentialGenerator {

    private static final String APP_ID_PREFIX = "app_";
    private static final String APP_KEY_PREFIX = "sk_";
    private static final int APP_ID_LENGTH = 32;
    private static final int APP_KEY_LENGTH = 64;
    private static final String HMAC_SECRET = "coocare_ai_app_secret_2025";
    
    /**
     * 生成应用ID (AppId)
     * 格式: app_ + 时间戳(8位) + 随机字符串(20位)
     * 
     * @return 生成的AppId
     */
    public String generateAppId() {
        try {
            // 获取当前时间戳的后8位
            String timestamp = String.valueOf(System.currentTimeMillis()).substring(5);
            
            // 生成20位随机字符串（包含数字和小写字母）
            String randomStr = RandomUtil.randomString("0123456789abcdefghijklmnopqrstuvwxyz", 20);
            
            String appId = APP_ID_PREFIX + timestamp + randomStr;
            
            log.info("Generated AppId: {}", appId);
            return appId;
        } catch (Exception e) {
            log.error("Failed to generate AppId", e);
            throw new RuntimeException("生成AppId失败", e);
        }
    }
    
    /**
     * 生成应用密钥 (AppKey)
     * 使用安全随机数生成器 + Base64编码
     * 
     * @param appId 应用ID，用于增强密钥的唯一性
     * @return 生成的AppKey
     */
    public String generateAppKey(String appId) {
        try {
            SecureRandom secureRandom = new SecureRandom();
            
            // 生成48字节的随机数据
            byte[] randomBytes = new byte[48];
            secureRandom.nextBytes(randomBytes);
            
            // 将appId作为盐值混入
            String saltedData = appId + Base64.getEncoder().encodeToString(randomBytes);
            
            // 使用SHA-256哈希
            String hashedKey = DigestUtil.sha256Hex(saltedData);
            
            // 截取前64位并添加前缀
            String appKey = APP_KEY_PREFIX + hashedKey.substring(0, APP_KEY_LENGTH - APP_KEY_PREFIX.length());
            
            log.info("Generated AppKey for AppId: {}", appId);
            return appKey;
        } catch (Exception e) {
            log.error("Failed to generate AppKey for AppId: {}", appId, e);
            throw new RuntimeException("生成AppKey失败", e);
        }
    }
    
    /**
     * 验证AppId格式是否正确
     * 
     * @param appId 待验证的AppId
     * @return 是否有效
     */
    public boolean validateAppIdFormat(String appId) {
        if (appId == null || appId.trim().isEmpty()) {
            return false;
        }
        
        // 检查前缀
        if (!appId.startsWith(APP_ID_PREFIX)) {
            return false;
        }
        
        // 检查长度
        if (appId.length() != APP_ID_LENGTH) {
            return false;
        }
        
        // 检查时间戳部分（第4-11位应该是数字）
        String timestampPart = appId.substring(4, 12);
        if (!timestampPart.matches("\\d{8}")) {
            return false;
        }
        
        // 检查随机字符串部分（第12-31位应该是数字和小写字母）
        String randomPart = appId.substring(12);
        if (!randomPart.matches("[0-9a-z]{20}")) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证AppKey格式是否正确
     * 
     * @param appKey 待验证的AppKey
     * @return 是否有效
     */
    public boolean validateAppKeyFormat(String appKey) {
        if (appKey == null || appKey.trim().isEmpty()) {
            return false;
        }
        
        // 检查前缀
        if (!appKey.startsWith(APP_KEY_PREFIX)) {
            return false;
        }
        
        // 检查长度
        if (appKey.length() != APP_KEY_LENGTH) {
            return false;
        }
        
        // 检查密钥部分是否为十六进制字符
        String keyPart = appKey.substring(APP_KEY_PREFIX.length());
        if (!keyPart.matches("[0-9a-f]+")) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 生成API签名
     * 用于验证API请求的合法性
     * 
     * @param appId 应用ID
     * @param appKey 应用密钥
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param data 请求数据
     * @return 签名字符串
     */
    public String generateSignature(String appId, String appKey, long timestamp, String nonce, String data) {
        try {
            // 构建待签名字符串
            String signString = String.format("appid=%s&timestamp=%d&nonce=%s&data=%s", 
                appId, timestamp, nonce, data != null ? data : "");
            
            // 使用HMAC-SHA256生成签名
            HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, appKey.getBytes(StandardCharsets.UTF_8));
            byte[] signBytes = hMac.digest(signString.getBytes(StandardCharsets.UTF_8));
            
            return Base64.getEncoder().encodeToString(signBytes);
        } catch (Exception e) {
            log.error("Failed to generate signature for AppId: {}", appId, e);
            throw new RuntimeException("生成签名失败", e);
        }
    }
    
    /**
     * 验证API签名
     * 
     * @param appId 应用ID
     * @param appKey 应用密钥
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param data 请求数据
     * @param signature 待验证的签名
     * @return 签名是否有效
     */
    public boolean verifySignature(String appId, String appKey, long timestamp, String nonce, 
                                 String data, String signature) {
        try {
            // 检查时间戳是否在有效期内（5分钟）
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - timestamp) > 5 * 60 * 1000) {
                log.warn("Timestamp expired for AppId: {}, timestamp: {}, current: {}", 
                    appId, timestamp, currentTime);
                return false;
            }
            
            // 生成期望的签名
            String expectedSignature = generateSignature(appId, appKey, timestamp, nonce, data);
            
            // 比较签名
            boolean isValid = expectedSignature.equals(signature);
            if (!isValid) {
                log.warn("Signature verification failed for AppId: {}", appId);
            }
            
            return isValid;
        } catch (Exception e) {
            log.error("Failed to verify signature for AppId: {}", appId, e);
            return false;
        }
    }
    
    /**
     * 生成随机nonce值
     * 
     * @return 随机nonce字符串
     */
    public String generateNonce() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成完整的应用凭证信息
     * 
     * @return 包含appId和appKey的凭证对象
     */
    public AppCredential generateCredentials() {
        String appId = generateAppId();
        String appKey = generateAppKey(appId);
        System.out.println(appKey);
        return new AppCredential(appId, appKey, System.currentTimeMillis());
    }
    
    /**
     * 应用凭证数据类
     */
    public static class AppCredential {
        private final String appId;
        private final String appKey;
        private final long createdTime;
        
        public AppCredential(String appId, String appKey, long createdTime) {
            this.appId = appId;
            this.appKey = appKey;
            this.createdTime = createdTime;
        }
        
        public String getAppId() {
            return appId;
        }
        
        public String getAppKey() {
            return appKey;
        }
        
        public long getCreatedTime() {
            return createdTime;
        }
        
        @Override
        public String toString() {
            return String.format("AppCredential{appId='%s', appKey='%s', createdTime=%d}", 
                appId, appKey.substring(0, 10) + "...", createdTime);
        }
    }

    public static void main(String[] args) {
        AppCredentialGenerator generator = new AppCredentialGenerator();
        AppCredential credential = generator.generateCredentials();
        System.out.println(credential);
    }
}
