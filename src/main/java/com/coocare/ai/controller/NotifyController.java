package com.coocare.ai.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.sign.template.SaSignMany;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.service.AiCustomerService;
import com.coocare.ai.service.PaymentNotifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 第三方支付回调接口控制器
 * <p>
 * 该控制器负责处理来自第三方支付平台的回调通知，包括：
 * 1. 支付成功回调处理
 * 2. 签名验证
 * 3. 回调数据记录和处理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-01
 */
@SaIgnore
@Slf4j
@Validated
@RestController
@RequestMapping("/notify")
@RequiredArgsConstructor
@Tag(name = "支付回调接口", description = "处理第三方支付平台的回调通知")
public class NotifyController {

    /**
     * 客户服务 - 用于处理客户相关业务逻辑
     */
    private final AiCustomerService customerService;

    /**
     * 支付回调服务 - 用于处理支付回调记录的保存和状态更新
     */
    private final PaymentNotifyService paymentNotifyService;


    /**
     * 处理第三方支付平台的支付成功回调通知
     * <p>
     * 该接口接收来自第三方支付平台的回调通知，执行以下操作：
     * 1. 验证请求签名的合法性
     * 2. 记录回调数据到数据库
     * 3. 返回处理结果给支付平台
     * </p>
     *
     * @param channel 支付渠道标识，用于区分不同的支付平台
     * @param sn 设备序列号，标识支付设备
     * @param price 支付金额，单位为分
     * @param timestamp 支付时间戳，Unix时间戳格式
     * @param sku 商品SKU，标识购买的商品
     * @param nonce 随机数，用于防重放攻击
     * @param sign 签名，用于验证请求的合法性
     * @return 统一响应结果，成功时返回操作成功信息
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当签名验证失败或数据保存失败时抛出
     */
    @Operation(
        summary = "支付成功回调",
        description = "接收第三方支付平台的支付成功回调通知，验证签名并记录回调数据"
    )
    @PostMapping("/{channel}")
    public AjaxResult<?> paymentNotify(
            @Parameter(description = "支付渠道标识", required = true, example = "alipay")
            @PathVariable @NotBlank(message = "支付渠道不能为空") String channel,

            @Parameter(description = "设备序列号", required = true, example = "SN123456789")
            @RequestParam @NotBlank(message = "设备序列号不能为空") String sn,

            @Parameter(description = "支付金额(分)", required = true, example = "1000")
            @RequestParam @NotNull(message = "支付金额不能为空") @Positive(message = "支付金额必须大于0") Long price,

            @Parameter(description = "支付时间戳", required = true, example = "1672531200")
            @RequestParam @NotNull(message = "时间戳不能为空") Long timestamp,

            @Parameter(description = "商品SKU", required = true, example = "PRODUCT_001")
            @RequestParam @NotBlank(message = "商品SKU不能为空") String sku,

            @Parameter(description = "随机数", required = true, example = "abc123def456")
            @RequestParam @NotBlank(message = "随机数不能为空") String nonce,

            @Parameter(description = "请求签名", required = true, example = "signature_hash")
            @RequestParam @NotBlank(message = "签名不能为空") String sign) {

        log.info("收到支付回调通知 - 渠道: {}, 设备SN: {}, 金额: {}分, 时间戳: {}, SKU: {}",
                channel, sn, price, timestamp, sku);

        try {
            // 1. 验证请求签名
            log.debug("开始验证支付回调签名 - 渠道: {}", channel);
            SaSignMany.getSignTemplate(channel).checkRequest(SaHolder.getRequest());
            log.debug("支付回调签名验证成功 - 渠道: {}", channel);

            // 2. 保存回调记录
            log.debug("开始保存支付回调记录 - 设备SN: {}", sn);
            paymentNotifyService.saveNotifyRecord(channel, sn, price, timestamp, sku);
            log.info("支付回调处理成功 - 渠道: {}, 设备SN: {}, 金额: {}分", channel, sn, price);

            return AjaxResult.ok();

        } catch (Exception e) {
            log.error("支付回调处理失败 - 渠道: {}, 设备SN: {}, 错误信息: {}", channel, sn, e.getMessage(), e);
            // 注意：支付回调通常需要返回成功状态给第三方，即使内部处理失败
            // 这里可以根据具体业务需求调整返回逻辑
            throw new RuntimeException("支付回调处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成支付回调测试参数
     * <p>
     * 该方法用于开发和测试环境，生成模拟的支付回调参数，
     * 包含签名信息，便于测试支付回调接口的功能。
     * </p>
     *
     * @return 包含签名的参数字符串
     * @apiNote 仅用于开发测试，生产环境应移除此接口
     */
    @Operation(
        summary = "生成测试参数",
        description = "生成用于测试支付回调的参数和签名（仅开发环境使用）"
    )
    @GetMapping("/test/generate-params")
    public AjaxResult<Map<String, Object>> generateTestParams() {
        log.warn("正在生成测试参数，请确保当前为开发环境");

        try {
            // 构建测试参数
            Map<String, Object> paramMap = new LinkedHashMap<>();
            paramMap.put("sn", "TEST_SN_" + System.currentTimeMillis());
            paramMap.put("price", 1000L);
            paramMap.put("timestamp", Instant.now().getEpochSecond());
            paramMap.put("sku", "TEST_SKU_001");
            paramMap.put("nonce", IdUtil.fastSimpleUUID());

            // 生成签名参数字符串
            String paramStr = SaSignMany.getSignTemplate("app_42883516wc41007asn4rzt4z1tad").addSignParamsAndJoin(paramMap);

            // 构建返回结果
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("params", paramMap);
            result.put("signedParams", paramStr);
            result.put("currentTimestamp", Instant.now().getEpochSecond());

            result.put("note", "此接口仅用于开发测试，生产环境请移除");

            log.info("测试参数生成成功: {}", paramStr);
            return AjaxResult.ok(result, "测试参数生成成功");

        } catch (Exception e) {
            log.error("生成测试参数失败: {}", e.getMessage(), e);
            return AjaxResult.failed("生成测试参数失败: " + e.getMessage());
        }
    }
}